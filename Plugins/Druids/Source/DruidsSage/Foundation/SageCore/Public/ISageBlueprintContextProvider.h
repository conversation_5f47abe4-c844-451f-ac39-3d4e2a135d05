#pragma once

#include "CoreMinimal.h"

class USageExtension;
class UBlueprint;

/**
 * Interface for providing Blueprint context information
 * This allows the DruidsSageEditorModule to provide context to other modules
 * without creating direct dependencies between them
 */
class SAGECORE_API ISageBlueprintContextProvider
{
public:
	virtual ~ISageBlueprintContextProvider() = default;

	/**
	 * Checks if a SageExtension Blueprint is currently being edited
	 * @return True if a SageExtension Blueprint is active in the editor
	 */
	virtual bool IsSageExtensionBlueprintActive() const = 0;

	/**
	 * Gets the currently active SageExtension Blueprint being edited
	 * @return The SageExtension CDO if a SageExtension Blueprint is currently open, nullptr otherwise
	 */
	virtual USageExtension* GetCurrentSageExtension() const = 0;

	/**
	 * Gets the Blueprint object for the currently active SageExtension
	 * @return The Blueprint object if a SageExtension Blueprint is currently open, nullptr otherwise
	 */
	virtual UBlueprint* GetCurrentSageExtensionBlueprint() const = 0;

	/**
	 * Gets the name of the currently active SageExtension Blueprint
	 * @return The Blueprint name, or empty string if none is active
	 */
	virtual FString GetCurrentSageExtensionBlueprintName() const = 0;
};
