#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "SageExtension.h"
#include "SageExtensionTypes.h"

#include "SageExtensionEditorUtilities.generated.h"

/**
 * Blueprint Function Library for SageExtension Editor Utility Widget
 * Provides access to the currently active SageExtension and related functionality
 */
UCLASS()
class SAGEEXTENSIONS_API USageExtensionEditorUtilities : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 * Gets the currently active SageExtension Blueprint being edited
	 * @return The SageExtension CDO if a SageExtension Blueprint is currently open, nullptr otherwise
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static USageExtension* GetCurrentSageExtension();

	/**
	 * Gets the Blueprint object for the currently active SageExtension
	 * @return The Blueprint object if a SageExtension Blueprint is currently open, nullptr otherwise
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static UBlueprint* GetCurrentSageExtensionBlueprint();

	/**
	 * Checks if a SageExtension Blueprint is currently being edited
	 * @return True if a SageExtension Blueprint is active in the editor
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static bool IsSageExtensionBlueprintActive();

	/**
	 * Gets the extension definition from the current SageExtension
	 * @return The extension definition, or empty struct if no SageExtension is active
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static FDruidsSageExtensionDefinition GetCurrentExtensionDefinition();

	/**
	 * Refreshes the current SageExtension (discovers actions and queries)
	 * @return True if refresh was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static bool RefreshCurrentSageExtension();

	/**
	 * Gets all Actions from the current SageExtension
	 * @return Array of action definitions
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static TArray<FDruidsSageExtensionActionDefinition> GetCurrentExtensionActions();

	/**
	 * Gets all Queries from the current SageExtension
	 * @return Array of query definitions
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static TArray<FDruidsSageExtensionQueryDefinition> GetCurrentExtensionQueries();

	/**
	 * Updates a parameter description for an Action in the current SageExtension
	 * @param ActionFunctionName The name of the action function
	 * @param ParameterName The name of the parameter to update
	 * @param NewDescription The new description text
	 * @return True if the update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static bool UpdateActionParameterDescription(const FString& ActionFunctionName, const FString& ParameterName, const FString& NewDescription);

	/**
	 * Updates a parameter description for a Query in the current SageExtension
	 * @param QueryFunctionName The name of the query function
	 * @param ParameterName The name of the parameter to update
	 * @param NewDescription The new description text
	 * @return True if the update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static bool UpdateQueryParameterDescription(const FString& QueryFunctionName, const FString& ParameterName, const FString& NewDescription);

	/**
	 * Updates the extension name for the current SageExtension
	 * @param NewName The new extension name
	 * @return True if the update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static bool UpdateExtensionName(const FString& NewName);

	/**
	 * Updates the extension description for the current SageExtension
	 * @param NewDescription The new extension description
	 * @return True if the update was successful
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static bool UpdateExtensionDescription(const FString& NewDescription);

	/**
	 * Gets the name of the currently active SageExtension Blueprint
	 * @return The Blueprint name, or empty string if none is active
	 */
	UFUNCTION(BlueprintCallable, Category = "Sage Extension Editor", CallInEditor = true)
	static FString GetCurrentSageExtensionBlueprintName();

private:
	/**
	 * Helper function to get the DruidsSageEditorModule
	 * @return Pointer to the editor module, or nullptr if not available
	 */
	static class FDruidsSageEditorModule* GetEditorModule();
};
