#include "SageExtensionEditorUtilities.h"
#include "DruidsSageEditorModule.h"
#include "Engine/Blueprint.h"
#include "LogDruids.h"

#if WITH_EDITOR
#include "Modules/ModuleManager.h"
#endif

FDruidsSageEditorModule* USageExtensionEditorUtilities::GetEditorModule()
{
#if WITH_EDITOR
	return FModuleManager::GetModulePtr<FDruidsSageEditorModule>("DruidsSageEditorModule");
#else
	return nullptr;
#endif
}

USageExtension* USageExtensionEditorUtilities::GetCurrentSageExtension()
{
#if WITH_EDITOR
	if (FDruidsSageEditorModule* EditorModule = GetEditorModule())
	{
		return EditorModule->GetCurrentSageExtension();
	}
#endif
	return nullptr;
}

UBlueprint* USageExtensionEditorUtilities::GetCurrentSageExtensionBlueprint()
{
#if WITH_EDITOR
	if (FDruidsSageEditorModule* EditorModule = GetEditorModule())
	{
		// Access the BlueprintHandler through the editor module
		// Note: You may need to add a public getter for BlueprintHandler in DruidsSageEditorModule
		// For now, we'll implement this through the existing GetCurrentSageExtension method
		USageExtension* CurrentExtension = EditorModule->GetCurrentSageExtension();
		if (CurrentExtension)
		{
			// Get the Blueprint from the extension's class
			if (UClass* ExtensionClass = CurrentExtension->GetClass())
			{
				return Cast<UBlueprint>(ExtensionClass->ClassGeneratedBy);
			}
		}
	}
#endif
	return nullptr;
}

bool USageExtensionEditorUtilities::IsSageExtensionBlueprintActive()
{
#if WITH_EDITOR
	if (FDruidsSageEditorModule* EditorModule = GetEditorModule())
	{
		return EditorModule->IsSageExtensionBlueprintActive();
	}
#endif
	return false;
}

FDruidsSageExtensionDefinition USageExtensionEditorUtilities::GetCurrentExtensionDefinition()
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	if (CurrentExtension)
	{
		TSharedPtr<FDruidsSageExtensionDefinition> Definition = CurrentExtension->GetExtensionDefinition();
		if (Definition.IsValid())
		{
			return *Definition;
		}
	}
	return FDruidsSageExtensionDefinition();
}

bool USageExtensionEditorUtilities::RefreshCurrentSageExtension()
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	if (CurrentExtension)
	{
		CurrentExtension->RefreshExtension();
		UE_LOG(LogDruidsSage, Log, TEXT("Refreshed SageExtension: %s"), *CurrentExtension->GetClass()->GetName());
		return true;
	}
	return false;
}

TArray<FDruidsSageExtensionActionDefinition> USageExtensionEditorUtilities::GetCurrentExtensionActions()
{
	FDruidsSageExtensionDefinition Definition = GetCurrentExtensionDefinition();
	return Definition.Actions;
}

TArray<FDruidsSageExtensionQueryDefinition> USageExtensionEditorUtilities::GetCurrentExtensionQueries()
{
	FDruidsSageExtensionDefinition Definition = GetCurrentExtensionDefinition();
	return Definition.Queries;
}

bool USageExtensionEditorUtilities::UpdateActionParameterDescription(const FString& ActionFunctionName, const FString& ParameterName, const FString& NewDescription)
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	if (!CurrentExtension)
	{
		return false;
	}

	// Get the extension definition (this returns a copy)
	TSharedPtr<FDruidsSageExtensionDefinition> Definition = CurrentExtension->GetExtensionDefinition();
	if (!Definition.IsValid())
	{
		return false;
	}

	// Find the action and update the parameter description
	for (FDruidsSageExtensionActionDefinition& Action : Definition->Actions)
	{
		if (Action.FunctionName.ToString() == ActionFunctionName)
		{
			for (FDruidsSageExtensionParameterDefinition& Parameter : Action.ParameterDefinitions)
			{
				if (Parameter.Name.ToString() == ParameterName)
				{
					Parameter.Description = NewDescription;
					// Mark the package as dirty to indicate changes
					CurrentExtension->MarkPackageDirty();
					UE_LOG(LogDruidsSage, Log, TEXT("Updated Action parameter description: %s.%s"), *ActionFunctionName, *ParameterName);
					return true;
				}
			}
		}
	}
	return false;
}

bool USageExtensionEditorUtilities::UpdateQueryParameterDescription(const FString& QueryFunctionName, const FString& ParameterName, const FString& NewDescription)
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	if (!CurrentExtension)
	{
		return false;
	}

	TSharedPtr<FDruidsSageExtensionDefinition> Definition = CurrentExtension->GetExtensionDefinition();
	if (!Definition.IsValid())
	{
		return false;
	}

	// Find the query and update the parameter description
	for (FDruidsSageExtensionQueryDefinition& Query : Definition->Queries)
	{
		if (Query.FunctionName.ToString() == QueryFunctionName)
		{
			for (FDruidsSageExtensionParameterDefinition& Parameter : Query.ParameterDefinitions)
			{
				if (Parameter.Name.ToString() == ParameterName)
				{
					Parameter.Description = NewDescription;
					CurrentExtension->MarkPackageDirty();
					UE_LOG(LogDruidsSage, Log, TEXT("Updated Query parameter description: %s.%s"), *QueryFunctionName, *ParameterName);
					return true;
				}
			}
		}
	}
	return false;
}

bool USageExtensionEditorUtilities::UpdateExtensionName(const FString& NewName)
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	if (!CurrentExtension)
	{
		return false;
	}

	TSharedPtr<FDruidsSageExtensionDefinition> Definition = CurrentExtension->GetExtensionDefinition();
	if (!Definition.IsValid())
	{
		return false;
	}

	Definition->ExtensionName = NewName;
	CurrentExtension->MarkPackageDirty();
	UE_LOG(LogDruidsSage, Log, TEXT("Updated Extension name to: %s"), *NewName);
	return true;
}

bool USageExtensionEditorUtilities::UpdateExtensionDescription(const FString& NewDescription)
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	if (!CurrentExtension)
	{
		return false;
	}

	TSharedPtr<FDruidsSageExtensionDefinition> Definition = CurrentExtension->GetExtensionDefinition();
	if (!Definition.IsValid())
	{
		return false;
	}

	Definition->ExtensionDescription = NewDescription;
	CurrentExtension->MarkPackageDirty();
	UE_LOG(LogDruidsSage, Log, TEXT("Updated Extension description"));
	return true;
}

FString USageExtensionEditorUtilities::GetCurrentSageExtensionBlueprintName()
{
	UBlueprint* CurrentBlueprint = GetCurrentSageExtensionBlueprint();
	if (CurrentBlueprint)
	{
		return CurrentBlueprint->GetName();
	}
	return FString();
}
